# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test results and artifacts
test-results/
playwright-report/
coverage/
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
docs/

# CI/CD
.github/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
dist/
build/
