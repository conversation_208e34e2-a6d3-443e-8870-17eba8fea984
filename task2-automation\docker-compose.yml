version: '3.8'

services:
  # Main test runner service
  playwright-tests:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-runner
    container_name: goodbudget-e2e-tests
    environment:
      - NODE_ENV=test
      - CI=true
      - BASE_URL=https://goodbudget.com
    volumes:
      - ./test-results:/app/test-results
      - ./tests:/app/tests:ro
      - ./playwright.config.ts:/app/playwright.config.ts:ro
    working_dir: /app
    command: npm test
    networks:
      - test-network

  # Chromium-specific tests
  test-chromium:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-chromium
    container_name: goodbudget-chromium-tests
    environment:
      - NODE_ENV=test
      - CI=true
      - BASE_URL=https://goodbudget.com
    volumes:
      - ./test-results:/app/test-results
      - ./tests:/app/tests:ro
      - ./playwright.config.ts:/app/playwright.config.ts:ro
    networks:
      - test-network

  # Firefox-specific tests
  test-firefox:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-firefox
    container_name: goodbudget-firefox-tests
    environment:
      - NODE_ENV=test
      - CI=true
      - BASE_URL=https://goodbudget.com
    volumes:
      - ./test-results:/app/test-results
      - ./tests:/app/tests:ro
      - ./playwright.config.ts:/app/playwright.config.ts:ro
    networks:
      - test-network

  # WebKit-specific tests
  test-webkit:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-webkit
    container_name: goodbudget-webkit-tests
    environment:
      - NODE_ENV=test
      - CI=true
      - BASE_URL=https://goodbudget.com
    volumes:
      - ./test-results:/app/test-results
      - ./tests:/app/tests:ro
      - ./playwright.config.ts:/app/playwright.config.ts:ro
    networks:
      - test-network

  # Parallel test execution across all browsers
  test-parallel:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-runner
    container_name: goodbudget-parallel-tests
    environment:
      - NODE_ENV=test
      - CI=true
      - BASE_URL=https://goodbudget.com
    volumes:
      - ./test-results:/app/test-results
      - ./tests:/app/tests:ro
      - ./playwright.config.ts:/app/playwright.config.ts:ro
    command: npm run test:parallel
    networks:
      - test-network

networks:
  test-network:
    driver: bridge

volumes:
  test-results:
    driver: local
