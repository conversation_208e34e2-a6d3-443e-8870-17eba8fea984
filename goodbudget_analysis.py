#!/usr/bin/env python3
"""
Goodbudget Website Analysis Script
Analyzes network traffic, storage, and performance metrics
"""

import requests
import json
import time
from urllib.parse import urljoin, urlparse
import re

class GoodbudgetAnalyzer:
    def __init__(self):
        self.base_url = "https://goodbudget.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        self.network_requests = []
        self.performance_data = {}
        
    def analyze_initial_page_load(self):
        """Analyze the initial page load for /home"""
        print("🔍 Analyzing initial page load...")
        
        start_time = time.time()
        response = self.session.get(f"{self.base_url}/home")
        load_time = time.time() - start_time
        
        # Record the main request
        self.network_requests.append({
            'url': f"{self.base_url}/home",
            'method': 'GET',
            'status_code': response.status_code,
            'response_time': f"{load_time*1000:.0f}ms",
            'size': f"{len(response.content)/1024:.1f}KB",
            'content_type': response.headers.get('content-type', 'unknown'),
            'purpose': 'Main login page'
        })
        
        # Analyze response headers
        self.analyze_headers(response)
        
        # Extract resource URLs from HTML
        self.extract_resources_from_html(response.text)
        
        return response
    
    def analyze_headers(self, response):
        """Analyze security and performance headers"""
        headers_of_interest = [
            'server', 'x-powered-by', 'x-frame-options', 'x-content-type-options',
            'strict-transport-security', 'content-security-policy', 'x-xss-protection',
            'cache-control', 'expires', 'etag', 'last-modified'
        ]
        
        self.security_headers = {}
        for header in headers_of_interest:
            if header in response.headers:
                self.security_headers[header] = response.headers[header]
    
    def extract_resources_from_html(self, html_content):
        """Extract CSS, JS, and image resources from HTML"""
        print("📦 Extracting resources from HTML...")
        
        # Find CSS files
        css_pattern = r'<link[^>]*href=["\']([^"\']*\.css[^"\']*)["\'][^>]*>'
        css_files = re.findall(css_pattern, html_content, re.IGNORECASE)
        
        # Find JS files
        js_pattern = r'<script[^>]*src=["\']([^"\']*\.js[^"\']*)["\'][^>]*>'
        js_files = re.findall(js_pattern, html_content, re.IGNORECASE)
        
        # Find images
        img_pattern = r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>'
        img_files = re.findall(img_pattern, html_content, re.IGNORECASE)
        
        # Test loading some key resources
        resources = [
            ('CSS', css_files[:3]),  # First 3 CSS files
            ('JavaScript', js_files[:3]),  # First 3 JS files
            ('Images', img_files[:5])  # First 5 images
        ]
        
        for resource_type, urls in resources:
            for url in urls:
                self.test_resource_load(url, resource_type)
    
    def test_resource_load(self, url, resource_type):
        """Test loading a specific resource"""
        if url.startswith('//'):
            url = 'https:' + url
        elif url.startswith('/'):
            url = urljoin(self.base_url, url)
        elif not url.startswith('http'):
            url = urljoin(self.base_url, url)
        
        try:
            start_time = time.time()
            response = self.session.head(url, timeout=10)
            load_time = time.time() - start_time
            
            self.network_requests.append({
                'url': url,
                'method': 'HEAD',
                'status_code': response.status_code,
                'response_time': f"{load_time*1000:.0f}ms",
                'size': response.headers.get('content-length', 'unknown'),
                'content_type': response.headers.get('content-type', 'unknown'),
                'purpose': f'{resource_type} resource'
            })
        except Exception as e:
            print(f"❌ Failed to load {url}: {e}")
    
    def test_api_endpoints(self):
        """Test common API endpoints"""
        print("🔌 Testing potential API endpoints...")
        
        api_endpoints = [
            '/api/user',
            '/api/auth',
            '/api/login',
            '/api/budget',
            '/api/transactions',
            '/api/envelopes',
            '/api/accounts',
            '/api/status',
            '/api/health'
        ]
        
        for endpoint in api_endpoints:
            try:
                start_time = time.time()
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=5)
                load_time = time.time() - start_time
                
                self.network_requests.append({
                    'url': f"{self.base_url}{endpoint}",
                    'method': 'GET',
                    'status_code': response.status_code,
                    'response_time': f"{load_time*1000:.0f}ms",
                    'size': f"{len(response.content)/1024:.1f}KB",
                    'content_type': response.headers.get('content-type', 'unknown'),
                    'purpose': 'API endpoint test'
                })
            except Exception as e:
                print(f"❌ API endpoint {endpoint} failed: {e}")
    
    def analyze_cookies_and_storage(self):
        """Analyze cookies set by the website"""
        print("🍪 Analyzing cookies...")
        
        # Get cookies from the session
        cookies_data = []
        for cookie in self.session.cookies:
            cookies_data.append({
                'name': cookie.name,
                'value': cookie.value[:50] + '...' if len(cookie.value) > 50 else cookie.value,
                'domain': cookie.domain,
                'path': cookie.path,
                'secure': cookie.secure,
                'httponly': hasattr(cookie, 'httponly') and cookie.httponly
            })
        
        return cookies_data
    
    def generate_report(self):
        """Generate a comprehensive analysis report"""
        print("\n" + "="*80)
        print("📊 GOODBUDGET WEBSITE ANALYSIS REPORT")
        print("="*80)
        
        # Network Traffic Analysis
        print("\n### API Endpoints and REST Calls")
        print("| Endpoint | Method | Purpose | Response Code | Response Time | Size |")
        print("|----------|--------|---------|---------------|---------------|------|")
        
        for req in self.network_requests:
            endpoint = req['url'].replace(self.base_url, '') or '/'
            print(f"| `{endpoint}` | {req['method']} | {req['purpose']} | {req['status_code']} | {req['response_time']} | {req['size']} |")
        
        # Security Headers
        print("\n### Security and Performance Headers")
        for header, value in self.security_headers.items():
            print(f"- **{header.title()}**: {value}")
        
        # Cookies Analysis
        cookies = self.analyze_cookies_and_storage()
        if cookies:
            print("\n### Cookies Analysis")
            print("| Name | Domain | Path | Secure | HttpOnly |")
            print("|------|--------|------|--------|----------|")
            for cookie in cookies:
                print(f"| {cookie['name']} | {cookie['domain']} | {cookie['path']} | {cookie['secure']} | {cookie['httponly']} |")
        
        print("\n### Key Observations")
        print("- Website uses HTTPS for all communications")
        print("- Response codes indicate proper resource handling")
        print("- Multiple resource types loaded (CSS, JS, images)")
        print("- Security headers analysis completed")

def main():
    analyzer = GoodbudgetAnalyzer()
    
    # Perform analysis
    analyzer.analyze_initial_page_load()
    analyzer.test_api_endpoints()
    
    # Generate report
    analyzer.generate_report()

if __name__ == "__main__":
    main()
