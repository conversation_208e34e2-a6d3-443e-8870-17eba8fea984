# N26 QA Engineer Assessment

This repository contains my solutions for the N26 QA Engineer technical assessment, demonstrating comprehensive testing skills across exploratory testing and test automation.

## 📋 Assessment Overview

### Task 1: Exploratory Testing Session
**File**: `task1.md`

Comprehensive exploratory testing of the Goodbudget web application including:
- **Testing Charters**: Structured approach to exploratory testing
- **Bug Findings**: 5 critical issues discovered with detailed reproduction steps
- **Risk Analysis**: Financial application-specific risk assessment
- **Technical Analysis**: Browser dev-tools investigation (Network, Application, Performance)

**Key Findings**:
- Issue 1: Uneven split transaction distribution (High severity)
- Issue 2: Confusing expense representation (High severity)
- Issue 4: Limited form validation for future dates (Medium severity)
- Security concerns: Missing critical security headers

### Task 2: Web UI Test Automation
**Folder**: `task2-automation/`

Production-ready E2E test automation framework using Playwright + TypeScript:

**Framework Features**:
- ✅ **TypeScript** implementation with strong typing
- ✅ **Cross-browser testing** (Chromium, Firefox, WebKit)
- ✅ **Docker-based execution** with multi-stage builds
- ✅ **GitHub Actions CI/CD** pipeline with parallel execution
- ✅ **Comprehensive reporting** (HTML, JSON, JUnit)

**Test Coverage**:
- **27 test cases** across 3 business-critical flows
- **Regression tests** for bugs discovered in Task 1
- **Cross-browser compatibility** validation
- **Accessibility and keyboard navigation** testing

## 🎯 Business-Critical Flows Tested

1. **User Authentication Flow** (9 tests)
   - Login/signup functionality
   - Form validation and security
   - Session management

2. **Single Transaction Management** (10 tests)
   - Core transaction creation
   - Data validation and persistence
   - Issue 4 regression testing

3. **Split Transaction Management** (8 tests)
   - Advanced budgeting features
   - Issue 1 regression testing
   - Complex form interactions

## 🏆 Technical Highlights

### Exploratory Testing Excellence
- **Structured charter-based approach** with clear prioritization
- **Real bug discovery** with detailed reproduction steps
- **Risk-based analysis** specific to financial applications
- **Technical depth** using browser dev-tools

### Test Automation Excellence
- **Modern tech stack** (Playwright + TypeScript)
- **Production-ready architecture** with Docker and CI/CD
- **Comprehensive test coverage** of critical user journeys
- **Bug validation** linking back to exploratory findings

## 🔧 Quick Start

### Task 1 - Review Exploratory Testing
```bash
# View the comprehensive testing report
cat task1.md
```

### Task 2 - Run Automation Framework
```bash
cd task2-automation

# Install dependencies
npm install

# Install browsers
npx playwright install

# Run all tests
npm test

# Run with Docker
docker-compose up test-parallel

# View test report
npm run test:report
```

## 📊 Results Summary

- **Task 1**: 5 bugs discovered, comprehensive risk analysis completed
- **Task 2**: 27 automated tests, 88.9% pass rate, full CI/CD pipeline
- **Integration**: Automation framework validates exploratory testing findings

## 🎯 Assessment Objectives Met

✅ **Exploratory Testing Skills**: Demonstrated through structured charter approach and real bug discovery  
✅ **Test Automation Expertise**: Modern framework with TypeScript, Docker, and CI/CD  
✅ **Risk Assessment**: Financial application-specific risk analysis  
✅ **Technical Depth**: Browser dev-tools analysis and performance evaluation  
✅ **Documentation**: Comprehensive documentation and reporting  
✅ **Industry Best Practices**: Professional-grade deliverables ready for production use  

## 🤝 Contact

For any questions about the implementation or approach, please feel free to reach out.

---

**Assessment Completed**: June 2025  
**Technologies Used**: Playwright, TypeScript, Docker, GitHub Actions, Chrome DevTools  
**Total Test Cases**: 27 automated + comprehensive exploratory coverage
