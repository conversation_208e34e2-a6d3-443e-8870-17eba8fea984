# Goodbudget Website Analysis Script
# PowerShell version for Windows environment

Write-Host "🔍 Starting Goodbudget Website Analysis..." -ForegroundColor Green

# Function to test endpoint and measure response time
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [string]$Purpose = "Unknown"
    )
    
    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        
        if ($Method -eq "HEAD") {
            $response = Invoke-WebRequest -Uri $Url -Method Head -ErrorAction Stop
        } else {
            $response = Invoke-WebRequest -Uri $Url -Method $Method -ErrorAction Stop
        }
        
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        $contentLength = if ($response.Headers.ContainsKey("Content-Length")) { 
            [math]::Round([int]$response.Headers["Content-Length"] / 1024, 1).ToString() + "KB"
        } else { 
            "Unknown" 
        }
        
        return @{
            Url = $Url
            Method = $Method
            Purpose = $Purpose
            StatusCode = $response.StatusCode
            ResponseTime = "${responseTime}ms"
            Size = $contentLength
            ContentType = $response.Headers["Content-Type"] -join ", "
            Success = $true
        }
    }
    catch {
        return @{
            Url = $Url
            Method = $Method
            Purpose = $Purpose
            StatusCode = $_.Exception.Response.StatusCode.value__
            ResponseTime = "Failed"
            Size = "N/A"
            ContentType = "N/A"
            Success = $false
        }
    }
}

# Test main endpoints
Write-Host "📊 Testing main endpoints..." -ForegroundColor Yellow

$endpoints = @(
    @{ Url = "https://goodbudget.com/home"; Purpose = "Main login page" },
    @{ Url = "https://goodbudget.com/"; Purpose = "Homepage" },
    @{ Url = "https://goodbudget.com/how-it-works/"; Purpose = "How it works page" },
    @{ Url = "https://goodbudget.com/signup"; Purpose = "Signup page" }
)

$results = @()
foreach ($endpoint in $endpoints) {
    $result = Test-Endpoint -Url $endpoint.Url -Purpose $endpoint.Purpose
    $results += $result
    Write-Host "✓ Tested: $($endpoint.Url)" -ForegroundColor Green
}

# Test potential API endpoints
Write-Host "🔌 Testing potential API endpoints..." -ForegroundColor Yellow

$apiEndpoints = @(
    "https://goodbudget.com/api/user",
    "https://goodbudget.com/api/auth",
    "https://goodbudget.com/api/login",
    "https://goodbudget.com/api/budget",
    "https://goodbudget.com/api/transactions",
    "https://goodbudget.com/api/envelopes",
    "https://goodbudget.com/api/accounts",
    "https://goodbudget.com/api/status",
    "https://goodbudget.com/api/health"
)

foreach ($apiUrl in $apiEndpoints) {
    $result = Test-Endpoint -Url $apiUrl -Purpose "API endpoint test"
    $results += $result
}

# Get detailed headers from main page
Write-Host "🔍 Analyzing headers from main page..." -ForegroundColor Yellow
$mainResponse = Invoke-WebRequest -Uri "https://goodbudget.com/home"

# Generate report
Write-Host "`n" + "="*80 -ForegroundColor Cyan
Write-Host "📊 GOODBUDGET WEBSITE ANALYSIS REPORT" -ForegroundColor Cyan
Write-Host "="*80 -ForegroundColor Cyan

Write-Host "`n### API Endpoints and REST Calls" -ForegroundColor White
Write-Host "| Endpoint | Method | Purpose | Response Code | Response Time | Size |"
Write-Host "|----------|--------|---------|---------------|---------------|------|"

foreach ($result in $results) {
    $endpoint = $result.Url -replace "https://goodbudget.com", ""
    if ($endpoint -eq "") { $endpoint = "/" }
    Write-Host "| ``$endpoint`` | $($result.Method) | $($result.Purpose) | $($result.StatusCode) | $($result.ResponseTime) | $($result.Size) |"
}

Write-Host "`n### Request/Response Headers Observed" -ForegroundColor White
Write-Host "- **Request URL**: https://goodbudget.com/home"
Write-Host "- **Request Method**: GET"
Write-Host "- **Status Code**: $($mainResponse.StatusCode) $($mainResponse.StatusDescription)"

Write-Host "`n#### Response Headers" -ForegroundColor Yellow
foreach ($header in $mainResponse.Headers.GetEnumerator()) {
    Write-Host "- **$($header.Key)**: $($header.Value -join ', ')"
}

Write-Host "`n### Performance Observations" -ForegroundColor White
Write-Host "- The application uses HTTPS for all communications"
Write-Host "- Website is served through CloudFront CDN (X-Amz-Cf-Pop: FRA6-C1)"
Write-Host "- Server: Apache/2.4.63 (Unix)"
Write-Host "- Cache-Control: max-age=0, must-revalidate, private"
Write-Host "- Content-Type: text/html; charset=UTF-8"

# Test for common static resources
Write-Host "`n### Static Resources Analysis" -ForegroundColor White
$staticResources = @(
    "https://goodbudget.com/wp-content/uploads/2019/03/29102844/GB-logo-wht.png",
    "https://goodbudget.com/wp-content/themes/goodbudget/style.css",
    "https://goodbudget.com/wp-includes/js/jquery/jquery.min.js"
)

Write-Host "| Resource | Type | Status | Response Time |"
Write-Host "|----------|------|--------|---------------|"

foreach ($resource in $staticResources) {
    $result = Test-Endpoint -Url $resource -Method "HEAD" -Purpose "Static resource"
    $resourceType = if ($resource -match "\.css") { "CSS" } 
                   elseif ($resource -match "\.js") { "JavaScript" }
                   elseif ($resource -match "\.(png|jpg|jpeg|gif|svg)") { "Image" }
                   else { "Unknown" }
    
    Write-Host "| ``$($resource -replace 'https://goodbudget.com', '')`` | $resourceType | $($result.StatusCode) | $($result.ResponseTime) |"
}

Write-Host "`n### Security Analysis" -ForegroundColor White
$securityHeaders = @("X-Frame-Options", "X-Content-Type-Options", "Strict-Transport-Security", "Content-Security-Policy", "X-XSS-Protection")
$foundSecurityHeaders = @()

foreach ($header in $securityHeaders) {
    if ($mainResponse.Headers.ContainsKey($header)) {
        $foundSecurityHeaders += "OK $header`: $($mainResponse.Headers[$header] -join ', ')"
    } else {
        $foundSecurityHeaders += "MISSING $header`: Not present"
    }
}

foreach ($header in $foundSecurityHeaders) {
    Write-Host "- $header"
}

Write-Host "`nAnalysis Complete!" -ForegroundColor Green
