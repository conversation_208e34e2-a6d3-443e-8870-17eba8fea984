{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "baseUrl": "./", "paths": {"@/*": ["./tests/*"], "@utils/*": ["./tests/utils/*"], "@setup/*": ["./tests/setup/*"]}}, "include": ["tests/**/*", "playwright.config.ts", "*.ts"], "exclude": ["node_modules", "dist", "test-results"]}