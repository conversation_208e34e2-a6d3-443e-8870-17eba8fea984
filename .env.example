# Environment Configuration for Goodbudget E2E Tests

# Application URL
BASE_URL=https://goodbudget.com

# Test Environment
NODE_ENV=test

# CI/CD Configuration
CI=false

# Test User Credentials (if needed for authenticated tests)
# TEST_USER_EMAIL=<EMAIL>
# TEST_USER_PASSWORD=testpassword123

# Browser Configuration
# HEADLESS=true
# BROWSER_TIMEOUT=30000

# Test Data Configuration
# TEST_DATA_CLEANUP=true
# TEST_HOUSEHOLD_PREFIX=TestHousehold

# Reporting Configuration
# REPORT_OUTPUT_DIR=test-results
# ENABLE_VIDEO_RECORDING=true
# ENABLE_SCREENSHOTS=true

# Debug Configuration
# DEBUG_MODE=false
# SLOW_MO=0

# Parallel Execution
# MAX_WORKERS=3
# MAX_FAILURES=5
