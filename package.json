{"name": "goodbudget-e2e-tests", "version": "1.0.0", "description": "End-to-end test automation framework for Goodbudget application using Playwright", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit", "test:parallel": "playwright test --workers=3", "test:report": "playwright show-report", "test:docker": "docker run --rm -v $(pwd):/workspace -w /workspace mcr.microsoft.com/playwright:v1.40.0-focal npm test", "install:browsers": "playwright install", "install:deps": "playwright install-deps"}, "keywords": ["playwright", "e2e", "testing", "automation", "goodbudget", "typescript"], "author": "Test Automation Engineer", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.8.0", "typescript": "^5.2.0"}, "dependencies": {"dotenv": "^16.3.1"}}